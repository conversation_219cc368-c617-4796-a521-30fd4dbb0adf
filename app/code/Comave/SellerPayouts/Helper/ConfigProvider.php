<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\SellerPayouts\Helper;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Customer;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Directory\Model\CurrencyFactory;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Framework\Message\ManagerInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class ConfigProvider extends AbstractHelper
{
    protected const XML_PATH_PAYOUTS_ENABLE = 'payouts/general/enable';
    protected const XML_ADMIN_STRIPE_MODE = 'payment/stripe_payments_basic/stripe_mode';
    protected const XML_ADMIN_STRIPE_ACCOUNT_ID = 'payouts/general/stripe_account_id';
    protected const XML_ADMIN_STRIPE_TEST_PUBLISHABLE_KEY = 'payment/stripe_payments_basic/stripe_test_pk';
    protected const XML_ADMIN_STRIPE_TEST_SECRET_KEY = 'payment/stripe_payments_basic/stripe_test_sk';
    protected const XML_ADMIN_STRIPE_LIVE_PUBLISHABLE_KEY = 'payment/stripe_payments_basic/stripe_live_pk';
    protected const XML_ADMIN_STRIPE_LIVE_SECRET_KEY = 'payment/stripe_payments_basic/stripe_live_sk';
    protected const XML_PATH_STRIPE_PAYOUTS_DATE = 'payouts/general/payout_schedule';
    protected const XML_PATH_PAYOUTS_AMOUNT = 'payouts/general/minimum_payout_amount';
    protected const XML_PATH_PAYOUTS_DATE = 'payouts/general/paypout_days';
    protected const XML_PATH_CUSTOM_DATE_ENABLED = 'payouts/general/is_custom_date_enable';

    protected const  XML_PATH_TEST_INSTANT_PAYMENTE = 'payouts/general/test_instant_payment';

    private const MIN_DAYS_SELLER_PAYOUTS = 28;
    private const MIN_DAYS_SELLER_PAYOUTS_AFTER_ORDER_DELIVERED = 14;

    /**
     * Construct function
     *
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Magento\Directory\Model\CurrencyFactory $currencyFactory
     * @param \Magento\Customer\Model\Session $_customerSession
     * @param \Magento\Framework\Encryption\EncryptorInterface $encryptor
     * @param \Magento\Customer\Model\Customer $customerModel
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     * @param \Magento\Framework\Message\ManagerInterface $messageManager
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        public CurrencyFactory $currencyFactory,
        protected CustomerSession $_customerSession,
        protected EncryptorInterface $encryptor,
        protected Customer $customerModel,
        protected StoreManagerInterface $storeManager,
        protected CustomerRepositoryInterface $customerRepository,
        protected ManagerInterface $messageManager,
        protected LoggerInterface $logger
    ) {
        parent::__construct($context);
    }

    /**
     * Get the minimum payout amounts to make a transaction function
     *
     * @param int|string|null $store
     * @return float
     */
    public function getMinPayoutAmount(int|string|null $store = null): float
    {
        $minAmount = $this->scopeConfig->getValue(self::XML_PATH_PAYOUTS_AMOUNT, ScopeInterface::SCOPE_STORE, $store);

        return !empty($minAmount) ? (float) $minAmount : 50.00;
    }

    /**
     * Check if Payout is Enabled function
     *
     * @param int|string|null $store
     * @return bool
     */
    public function isEnabled(int|string|null $store = null): bool
    {
        return $this->scopeConfig->isSetFlag(self::XML_PATH_PAYOUTS_ENABLE, ScopeInterface::SCOPE_STORE, $store);
    }

    /**
     * Check if Testing mode Payout is Enabled function
     *
     * @param int|string|null $store
     * @return bool
     */
    public function isTestEnabled(int|string|null $store = null): bool
    {
        return $this->scopeConfig->isSetFlag(self::XML_PATH_TEST_INSTANT_PAYMENTE, ScopeInterface::SCOPE_STORE, $store);
    }

    /**
     * Get the days to make a payout to seller after order delivered function
     *
     * @param int|string|null $store
     * @return int
     */
    public function getDate(int|string|null $store = null): int
    {
        if($this->isTestEnabled()){
            return 0;
        }

        $minDays = $this->scopeConfig->getValue(self::XML_PATH_PAYOUTS_DATE, ScopeInterface::SCOPE_STORE, $store);

        return !empty($minDays) ? (int) $minDays : self::MIN_DAYS_SELLER_PAYOUTS_AFTER_ORDER_DELIVERED;
    }

    /**
     * Get Custom Days function
     *
     * @return mixed
     */
    public function getCustomeDays(): mixed
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_CUSTOM_DATE_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $this->storeManager->getStore()->getStoreId()
        );
    }

    /**
     * Get the days to make a payout to seller from the seller dashboard
     *
     * @param int|string|null $store
     * @return int
     */
    public function getSellerPayoutsDays(int|string|null $store = null): int
    {
        $days = $this->scopeConfig->getValue(self::XML_PATH_STRIPE_PAYOUTS_DATE, ScopeInterface::SCOPE_STORE, $store);

        if (empty($days)) {
            return self::MIN_DAYS_SELLER_PAYOUTS;
        }

        return (int) $days;
    }

    /**
     * Get Platform Stripe Id function
     *
     * @return mixed
     */
    public function getPlatformStripeId(): mixed
    {
        return $this->scopeConfig->getValue(
            self::XML_ADMIN_STRIPE_ACCOUNT_ID,
            ScopeInterface::SCOPE_STORE,
            $this->storeManager->getStore()->getStoreId()
        );
    }

    /**
     * Get Store Base Currency function
     *
     * @return string|null
     */
    public function getStoreBaseCurrency(): string|null
    {
        return $this->scopeConfig->getValue(
            \Magento\Directory\Model\Currency::XML_PATH_CURRENCY_BASE,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }
}
