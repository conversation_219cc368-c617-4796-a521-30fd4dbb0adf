<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="seller_payouts" translate="label" sortOrder="999">
            <label>Comave Seller PayOuts</label>
        </tab>
        <section id="payouts" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
            <label>General</label>
            <tab>seller_payouts</tab>
            <resource>Comave_SellerPayouts::transcation_payout</resource>
            <group id="general" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                <label>General Configuration</label>
                <field id="enable" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable or disable the feature</comment>
                </field>
                <field id="test_instant_payment" type="select" sortOrder="15" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Enable Test Instant Payments</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Overwrites collection limit of n days after last order updates</comment>
                </field>
                <field id="stripe_account_id" type="text" sortOrder="20" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Stripe Account ID</label>
                    <comment>Enter your Stripe account ID. Make sure the Stripe account ID matches the account details configured in your Stripe payment method.</comment>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="is_custom_date_enable" type="select" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Allow instanst payout to seller from admin</comment>
                </field>
                <field id="paypout_days" type="text" sortOrder="20" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Seller PayOuts Days</label>
                    <comment>Please enter the day to payouts seller after order deliver. By Default 14 days</comment>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="payout_schedule" type="select" sortOrder="50" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Payout Schedule</label>
                    <comment>Choose the date for payouts to sellers by default it 28 Seller will get payout on every month of that date.</comment>
                    <source_model>Comave\SellerPayouts\Model\Config\Source\PayoutSchedule</source_model>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="minimum_payout_amount" type="text" sortOrder="60" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
                    <label>Minimum Payout Amount</label>
                    <comment>Enter the minimum payout amount for sellers by default it 50 dollers.</comment>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
