<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\SellerPayouts\Cron;

use Comave\Logger\Model\ComaveLogger;
use Comave\SellerPayouts\Helper\Data as DataHelper;
use Comave\SellerPayouts\Helper\Payout as PayoutHelper;
use Comave\SellerPayouts\Helper\Payout\Collections as PayoutCollectionsHelper;
use Laminas\ReCaptcha\Exception;
use Webkul\Marketplace\Model\OrdersFactory;

/**
 * Class PayOuts used to pay sellers.
 */
class PayOuts
{
    /**
     * Construct function
     *
     * @param \Comave\SellerPayouts\Helper\Data $dataHelper
     * @param \Comave\SellerPayouts\Helper\Payout $payoutHelper
     * @param \Comave\SellerPayouts\Helper\Payout\Collections $payoutCollectionsHelper
     * @param \Webkul\Marketplace\Model\OrdersFactory $ordersModel
     * @param \Comave\Logger\Model\ComaveLogger $logger
     */
    public function __construct(
        protected DataHelper $dataHelper,
        protected PayoutHelper $payoutHelper,
        protected PayoutCollectionsHelper $payoutCollectionsHelper,
        protected OrdersFactory $ordersModel,
        protected ComaveLogger $logger
    ) {
    }

    /**
     * Execute action.
     *
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException|\Exception
     */
    public function execute(): void
    {
        if (!$this->dataHelper->isEnabled()) {
            return;
        }

        $sellerArr = $this->payoutCollectionsHelper->getSellerCollection();
        $sellerIds = array_unique($sellerArr->getColumnValues('seller_id'));

        foreach ($sellerIds as $sellerId) {
            try {
                $this->processSeller((int) $sellerId);
            } catch (Exception $exception){
                $this->logger->info("Error in processing seller {$sellerId}. Error: {$exception->getMessage()}");
                continue;
            }
        }
    }

    /**
     * Process Seller function
     *
     * @param int $sellerId
     * @return void
     */
    public function processSeller(int $sellerId): void
    {
        $collection = $this->payoutCollectionsHelper->getSellerCollectionForDeliveredItems($sellerId);

        if ($collection->getSize() === 0) {
            $this->logger->info("No Payout orders for Seller {$sellerId}");

            return;
        }

        $minAmounts = $this->dataHelper->getMinPayoutAmount();
        [$total, $cost, $info, $costDisplay] = $this->payoutHelper->calculatePayoutAmount($collection, $sellerId);

        if ($cost < $minAmounts) {
            $this->logger->info("Minimum Amount should be {$minAmounts} to make a payout to seller {$sellerId}.");

            return;
        }

        $ordersIds = implode(',', $collection->getColumnValues('entity_id'));

        if (!$this->dataHelper->manualPayout($cost, $sellerId, $ordersIds)) {
            $this->logger->info("Please pay with the manual pay (Seller Id: {$sellerId}, Orders: $ordersIds.");

            return;
        }

        $this->payoutHelper->handleSellerSales($sellerId, $cost, $total, $collection, $info, $costDisplay, 'Auto Pay');
        $this->logger->info("Payment has been successfully done for this seller id {$sellerId}");
    }
}
