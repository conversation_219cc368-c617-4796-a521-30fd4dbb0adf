<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Model\Config\Backend;

use Magento\Framework\App\Config\Value;
use Magento\Framework\App\Config\ValueFactory;
use Magento\Framework\App\ResourceConnection;
use Magento\Cron\Model\Schedule;
use Magento\Cron\Model\Config as CronConfig;

class SyncFrequency extends Value
{
    private const CRON_STRING_PATH = 'crontab/default/jobs/comave_payout_management_sync/schedule/cron_expr';
    private const CRON_MODEL_PATH = 'crontab/default/jobs/comave_payout_management_sync/run/model';

    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\App\Config\ScopeConfigInterface $config,
        \Magento\Framework\App\Cache\TypeListInterface $cacheTypeList,
        private readonly ValueFactory $configValueFactory,
        private readonly ResourceConnection $resourceConnection,
        private readonly CronConfig $cronConfig,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct($context, $registry, $config, $cacheTypeList, $resource, $resourceCollection, $data);
    }

    /**
     * Update cron expression when frequency changes
     *
     * @return $this
     * @throws \Exception
     */
    public function afterSave()
    {
        $frequency = $this->getValue();

        if ($frequency) {
            try {
                // Clear old schedules first
                $this->clearOldSchedules();

                // Update cron configuration
                $this->configValueFactory->create()->load(
                    self::CRON_STRING_PATH,
                    'path'
                )->setValue(
                    $frequency
                )->setPath(
                    self::CRON_STRING_PATH
                )->save();

                $this->configValueFactory->create()->load(
                    self::CRON_MODEL_PATH,
                    'path'
                )->setValue(
                    'Comave\PayoutManagement\Cron\SyncPayouts::execute'
                )->setPath(
                    self::CRON_MODEL_PATH
                )->save();

                // Force cache clean to ensure config changes are picked up
                $this->_cacheTypeList->cleanType('config');

                // Create immediate schedule to bridge the gap
                $this->createImmediateSchedule($frequency);

                // Try to generate additional schedules using Magento's system
                $this->generateSchedules();

                $this->_logger->info('Updated cron expression for payout sync', [
                    'frequency' => $frequency,
                    'cron_path' => self::CRON_STRING_PATH,
                    'cache_cleaned' => true
                ]);

            } catch (\Exception $e) {
                $this->_logger->error('Error updating cron expression: ' . $e->getMessage());
                throw new \Magento\Framework\Exception\CouldNotSaveException(__('We can\'t save the cron expression.'));
            }
        }

        return parent::afterSave();
    }

    /**
     * Clear old pending schedules when frequency changes
     *
     * @return void
     */
    private function clearOldSchedules(): void
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $tableName = $this->resourceConnection->getTableName('cron_schedule');

            $deletedCount = $connection->delete($tableName, [
                'job_code = ?' => 'comave_payout_management_sync',
                'status = ?' => 'pending'
            ]);

            $this->_logger->info('Cleared old cron schedules', [
                'job_code' => 'comave_payout_management_sync',
                'deleted_count' => $deletedCount
            ]);

        } catch (\Exception $e) {
            $this->_logger->error('Error clearing old schedules: ' . $e->getMessage());
        }
    }

    /**
     * Create an immediate schedule to bridge the gap until Magento generates more
     *
     * @param string $frequency
     * @return void
     */
    private function createImmediateSchedule(string $frequency): void
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $tableName = $this->resourceConnection->getTableName('cron_schedule');

            // Calculate next run time based on frequency
            $nextRunTime = $this->calculateNextRunTime($frequency);
            $scheduledAt = date('Y-m-d H:i:s', $nextRunTime);
            $createdAt = date('Y-m-d H:i:s');

            // Check if a schedule already exists for this time to avoid duplicates
            $existingSchedule = $connection->fetchOne(
                "SELECT schedule_id FROM {$tableName} WHERE job_code = ? AND scheduled_at = ? AND status = 'pending'",
                ['comave_payout_management_sync', $scheduledAt]
            );

            if (!$existingSchedule) {
                $insertResult = $connection->insert($tableName, [
                    'job_code' => 'comave_payout_management_sync',
                    'status' => 'pending',
                    'scheduled_at' => $scheduledAt,
                    'created_at' => $createdAt
                ]);

                $this->_logger->info('Created immediate cron schedule', [
                    'job_code' => 'comave_payout_management_sync',
                    'frequency' => $frequency,
                    'next_run' => $scheduledAt,
                    'insert_result' => $insertResult,
                    'schedule_id' => $connection->lastInsertId()
                ]);
            } else {
                $this->_logger->info('Immediate cron schedule already exists', [
                    'job_code' => 'comave_payout_management_sync',
                    'scheduled_at' => $scheduledAt,
                    'existing_schedule_id' => $existingSchedule
                ]);
            }

        } catch (\Exception $e) {
            $this->_logger->error('Error creating immediate schedule: ' . $e->getMessage(), [
                'frequency' => $frequency,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Calculate the next run time based on cron expression
     * For immediate transition, we use shorter intervals to avoid long waits
     *
     * @param string $cronExpression
     * @return int
     */
    private function calculateNextRunTime(string $cronExpression): int
    {
        $now = time();

        switch ($cronExpression) {
            case '* * * * *': // Every minute
                return $now + 60;
            case '*/15 * * * *': // Every 15 minutes
                return $now + (2 * 60); // Start in 2 minutes for immediate effect
            case '0 * * * *': // Every hour
                return $now + (5 * 60); // Start in 5 minutes for immediate effect
            case '0 */6 * * *': // Every 6 hours
                return $now + (10 * 60); // Start in 10 minutes for immediate effect
            case '0 */12 * * *': // Every 12 hours
                return $now + (10 * 60); // Start in 10 minutes for immediate effect
            case '0 0 * * *': // Daily
                return $now + (15 * 60); // Start in 15 minutes for immediate effect
            case '0 0 * * 0': // Weekly
                return $now + (15 * 60); // Start in 15 minutes for immediate effect
            default:
                return $now + (5 * 60); // Default: 5 minutes for immediate effect
        }
    }

    /**
     * Generate additional schedules using Magento's cron system
     *
     * @return void
     */
    private function generateSchedules(): void
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $tableName = $this->resourceConnection->getTableName('cron_schedule');

            // Get the job configuration
            $jobs = $this->cronConfig->getJobs();
            $jobConfig = $jobs['default']['comave_payout_management_sync'] ?? null;

            if (!$jobConfig) {
                $this->_logger->warning('Job configuration not found for comave_payout_management_sync');
                return;
            }

            // Get the current cron expression from config
            $cronExpr = $this->_config->getValue(self::CRON_STRING_PATH);
            if (!$cronExpr) {
                $this->_logger->warning('Cron expression not found in config');
                return;
            }

            // Generate a few future schedules manually
            $scheduleAheadFor = 4; // Generate 4 schedules ahead
            $now = time();

            for ($i = 1; $i <= $scheduleAheadFor; $i++) {
                $nextTime = $this->calculateFutureRunTime($cronExpr, $now, $i);
                $scheduledAt = date('Y-m-d H:i:s', $nextTime);

                // Check if schedule already exists
                $existingSchedule = $connection->fetchOne(
                    "SELECT schedule_id FROM {$tableName} WHERE job_code = ? AND scheduled_at = ? AND status = 'pending'",
                    ['comave_payout_management_sync', $scheduledAt]
                );

                if (!$existingSchedule) {
                    $connection->insert($tableName, [
                        'job_code' => 'comave_payout_management_sync',
                        'status' => 'pending',
                        'scheduled_at' => $scheduledAt,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);

                    $this->_logger->info('Generated additional cron schedule', [
                        'job_code' => 'comave_payout_management_sync',
                        'scheduled_at' => $scheduledAt,
                        'sequence' => $i
                    ]);
                }
            }

        } catch (\Exception $e) {
            $this->_logger->error('Error generating additional schedules: ' . $e->getMessage());
        }
    }

    /**
     * Calculate future run time based on cron expression
     *
     * @param string $cronExpr
     * @param int $baseTime
     * @param int $sequence
     * @return int
     */
    private function calculateFutureRunTime(string $cronExpr, int $baseTime, int $sequence): int
    {
        switch ($cronExpr) {
            case '* * * * *': // Every minute
                return $baseTime + (60 * $sequence);
            case '*/15 * * * *': // Every 15 minutes
                return $baseTime + (15 * 60 * $sequence);
            case '0 * * * *': // Every hour
                return $baseTime + (60 * 60 * $sequence);
            case '0 */6 * * *': // Every 6 hours
                return $baseTime + (6 * 60 * 60 * $sequence);
            case '0 */12 * * *': // Every 12 hours
                return $baseTime + (12 * 60 * 60 * $sequence);
            case '0 0 * * *': // Daily
                return $baseTime + (24 * 60 * 60 * $sequence);
            case '0 0 * * 0': // Weekly
                return $baseTime + (7 * 24 * 60 * 60 * $sequence);
            default:
                return $baseTime + (60 * 60 * $sequence); // Default: hourly
        }
    }
}
