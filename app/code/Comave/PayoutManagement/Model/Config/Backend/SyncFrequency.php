<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Model\Config\Backend;

use Magento\Framework\App\Config\Value;
use Magento\Framework\App\Config\ValueFactory;
use Magento\Framework\App\ResourceConnection;
use Magento\Cron\Model\Schedule;

class SyncFrequency extends Value
{
    private const CRON_STRING_PATH = 'crontab/default/jobs/comave_payout_management_sync/schedule/cron_expr';
    private const CRON_MODEL_PATH = 'crontab/default/jobs/comave_payout_management_sync/run/model';

    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\App\Config\ScopeConfigInterface $config,
        \Magento\Framework\App\Cache\TypeListInterface $cacheTypeList,
        private readonly ValueFactory $configValueFactory,
        private readonly ResourceConnection $resourceConnection,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct($context, $registry, $config, $cacheTypeList, $resource, $resourceCollection, $data);
    }

    /**
     * Update cron expression when frequency changes
     *
     * @return $this
     * @throws \Exception
     */
    public function afterSave()
    {
        $frequency = $this->getValue();

        if ($frequency) {
            try {
                $this->clearOldSchedules();
                $this->configValueFactory->create()->load(
                    self::CRON_STRING_PATH,
                    'path'
                )->setValue(
                    $frequency
                )->setPath(
                    self::CRON_STRING_PATH
                )->save();

                $this->configValueFactory->create()->load(
                    self::CRON_MODEL_PATH,
                    'path'
                )->setValue(
                    'Comave\PayoutManagement\Cron\SyncPayouts::execute'
                )->setPath(
                    self::CRON_MODEL_PATH
                )->save();

                $this->_logger->info('Updated cron expression for payout sync', [
                    'frequency' => $frequency,
                    'cron_path' => self::CRON_STRING_PATH
                ]);

                $this->createNewSchedules($frequency);

            } catch (\Exception $e) {
                $this->_logger->error('Error updating cron expression: ' . $e->getMessage());
                throw new \Magento\Framework\Exception\CouldNotSaveException(__('We can\'t save the cron expression.'));
            }
        }

        return parent::afterSave();
    }

    /**
     * Clear old pending schedules when frequency changes
     *
     * @return void
     */
    private function clearOldSchedules(): void
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $tableName = $this->resourceConnection->getTableName('cron_schedule');

            $deletedCount = $connection->delete($tableName, [
                'job_code = ?' => 'comave_payout_management_sync',
                'status = ?' => 'pending'
            ]);

            $this->_logger->info('Cleared old cron schedules', [
                'job_code' => 'comave_payout_management_sync',
                'deleted_count' => $deletedCount
            ]);

        } catch (\Exception $e) {
            $this->_logger->error('Error clearing old schedules: ' . $e->getMessage());
        }
    }

    /**
     * Create new schedules with the updated frequency
     *
     * @param string $frequency
     * @return void
     */
    private function createNewSchedules(string $frequency): void
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $tableName = $this->resourceConnection->getTableName('cron_schedule');

            $nextScheduleTime = $this->getNextScheduleTime($frequency);

            $connection->insert($tableName, [
                'job_code' => 'comave_payout_management_sync',
                'status' => 'pending',
                'scheduled_at' => date('Y-m-d H:i:s', $nextScheduleTime),
                'created_at' => date('Y-m-d H:i:s')
            ]);

            $this->_logger->info('Created new cron schedule', [
                'job_code' => 'comave_payout_management_sync',
                'frequency' => $frequency,
                'next_run' => date('Y-m-d H:i:s', $nextScheduleTime)
            ]);

        } catch (\Exception $e) {
            $this->_logger->error('Error creating new schedules: ' . $e->getMessage());
        }
    }

    /**
     * Get the next schedule time based on cron expression
     *
     * @param string $cronExpression
     * @return int
     */
    private function getNextScheduleTime(string $cronExpression): int
    {
        $now = time();

        switch ($cronExpression) {
            case '* * * * *':
                return $now + 60;
            case '*/15 * * * *':
                return $now + (15 * 60);
            case '0 * * * *':
                return $now + (60 * 60);
            case '0 */6 * * *':
                return $now + (6 * 60 * 60);
            case '0 */12 * * *':
                return $now + (12 * 60 * 60);
            case '0 0 * * *':
                return $now + (24 * 60 * 60);
            case '0 0 * * 0':
                return $now + (7 * 24 * 60 * 60);
            default:
                return $now + (60 * 60);
        }
    }
}
